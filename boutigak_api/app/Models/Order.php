<?php

namespace App\Models;

use App\Models\Item;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $table = 'order';


    protected $with = ['user' , 'orderItems.originalItem.images'];

    protected $fillable = [
        'user_id',
        'store_id',
        'status',
        'modified_by_store',
        'modification_reason_en',
        'modification_reason_fr',
        'modification_reason_ar',
        'delivery_charge',
        'delivery_charge_id',
        'location_id',
        'item_id',
        'total',
        'is_paid',
        'is_cash_on_delivery'
    ];

    protected $casts = [
        'modified_by_store' => 'boolean',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
    ];

    public function user(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function store(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function orderPayment(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(OrderPayment::class);
    }

    public function location(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Location::class);
    }

    public function modifications(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(OrderModification::class);
    }

    public function item(){
        return $this->belongsTo(Item::class);

    }

    public function items(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Item::class, 'order_item')
                    ->withPivot('quantity', 'price')
                    ->withTimestamps();
    }

    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }
}
